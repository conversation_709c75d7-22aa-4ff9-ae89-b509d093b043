unit Main.Form;

interface

uses
  Winapi.Windows, Winapi.Messages, Winapi.ActiveX, Winapi.ShlObj, Winapi.ShellAPI,
  System.SysUtils, System.Variants, System.Classes, System.Win.ComObj,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Vcl.Graphics, Vcl.StdCtrls;

type
  TOutlookDropTarget = class(TInterfacedObject, IDropTarget)
  private
    FOwner: TPanel;
    FOnDropFiles: TNotifyEvent;
    FDataObject: IDataObject; // Store data object for extraction
  public
    constructor Create(AOwner: TPanel);
    function DragEnter(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
      stdcall;
    function DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;
    function DragLeave: HResult; stdcall;
    function Drop(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
      stdcall;
    property OnDropFiles: TNotifyEvent read FOnDropFiles write FOnDropFiles;
    property DataObject: IDataObject read FDataObject;
  end;

  TFormMain = class(TForm)
    Memo1: TMemo;
    Panel1: TPanel;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
  private
    FDropTarget: TOutlookDropTarget;
    procedure HandleOutlookDrop(Sender: TObject);
  public
    { Public declarations }
  end;

var
  FormMain: TFormMain;

implementation

{$R *.dfm}

const
  C_OUTLOOK_MSG_FORMAT = 'FileGroupDescriptorW'; // Use Unicode version
  C_OUTLOOK_CONTENT_FORMAT = 'FileContents';

  { TOutlookDropTarget }

constructor TOutlookDropTarget.Create(AOwner: TPanel);
begin
  inherited Create;
  FOwner := AOwner;
end;

function TOutlookDropTarget.DragEnter(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect:
  Longint): HResult;
var
  LFormatEtc: TFormatEtc;
begin
  // Check for Outlook message format
  LFormatEtc.cfFormat := RegisterClipboardFormat(C_OUTLOOK_MSG_FORMAT);
  LFormatEtc.ptd := nil;
  LFormatEtc.dwAspect := DVASPECT_CONTENT;
  LFormatEtc.lindex := -1;
  LFormatEtc.tymed := TYMED_HGLOBAL;

  if dataObj.QueryGetData(LFormatEtc) = S_OK then
    dwEffect := DROPEFFECT_COPY
  else
    dwEffect := DROPEFFECT_NONE;

  Result := S_OK;
end;

function TOutlookDropTarget.DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
begin
  dwEffect := DROPEFFECT_COPY;
  Result := S_OK;
end;

function TOutlookDropTarget.DragLeave: HResult;
begin
  Result := S_OK;
end;

function TOutlookDropTarget.Drop(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint):
  HResult;
begin
  dwEffect := DROPEFFECT_COPY;
  FDataObject := dataObj; // Store for extraction
  if Assigned(FOnDropFiles) then
    FOnDropFiles(Self);
  Result := S_OK;
end;

{ TFormMain }

procedure TFormMain.FormCreate(Sender: TObject);
begin
  OleInitialize(nil);
  FDropTarget := TOutlookDropTarget.Create(Panel1);
  FDropTarget.OnDropFiles := HandleOutlookDrop;
  RegisterDragDrop(Panel1.Handle, FDropTarget);
end;

procedure TFormMain.FormDestroy(Sender: TObject);
begin
  if Assigned(FDropTarget) then
  begin
    RevokeDragDrop(Panel1.Handle);
    FDropTarget := nil;
  end;
  OleUninitialize;
end;

procedure TFormMain.HandleOutlookDrop(Sender: TObject);
var
  LDropTarget: TOutlookDropTarget;
  LFormatEtc: TFormatEtc;
  LMedium: TStgMedium;
  LFileGroupDesc: PFileGroupDescriptorW;
  LFileName: string;
  LOutlookApp: OleVariant;
  LSelection: OleVariant;
  LMailItem: OleVariant;
  i: Integer;
  LBody: string;
begin
  LDropTarget := Sender as TOutlookDropTarget;
  
  try
    // Try to get Outlook application
    try
      LOutlookApp := GetActiveOleObject('Outlook.Application');
    except
      LOutlookApp := CreateOleObject('Outlook.Application');
    end;
    
    // Get current selection (the dragged item)
    LSelection := LOutlookApp.ActiveExplorer.Selection;
    if LSelection.Count > 0 then
    begin
      LMailItem := LSelection.Item(1);
      
      Memo1.Lines.Add(Format('Mail dropped at %s', [TimeToStr(Now)]));
      Memo1.Lines.Add(Format('From: %s', [string(LMailItem.SenderName)]));
      Memo1.Lines.Add(Format('Email: %s', [string(LMailItem.SenderEmailAddress)]));
      Memo1.Lines.Add(Format('Subject: %s', [string(LMailItem.Subject)]));
      Memo1.Lines.Add(Format('Received: %s', [string(LMailItem.ReceivedTime)]));
      Memo1.Lines.Add(Format('Size: %d bytes', [Integer(LMailItem.Size)]));
      
      // Recipients
      if LMailItem.Recipients.Count > 0 then
      begin
        Memo1.Lines.Add('Recipients:');
        for i := 1 to LMailItem.Recipients.Count do
          Memo1.Lines.Add(Format('  - %s (%s)', [string(LMailItem.Recipients.Item(i).Name), string(LMailItem.Recipients.Item(i).Address)]));
      end;
      
      // Attachments
      if LMailItem.Attachments.Count > 0 then
      begin
        Memo1.Lines.Add('Attachments:');
        for i := 1 to LMailItem.Attachments.Count do
          Memo1.Lines.Add(Format('  - %s (%d bytes)', [string(LMailItem.Attachments.Item(i).FileName), Integer(LMailItem.Attachments.Item(i).Size)]));
      end;
      
      // Body preview (first 200 characters)
      LBody := string(LMailItem.Body);
      if Length(LBody) > 200 then
        LBody := Copy(LBody, 1, 200) + '...';
      Memo1.Lines.Add('Body preview:');
      Memo1.Lines.Add(LBody);
      Memo1.Lines.Add('---');
    end
    else
    begin
      // Fallback to filename extraction
      LFormatEtc.cfFormat := RegisterClipboardFormat(C_OUTLOOK_MSG_FORMAT);
      LFormatEtc.ptd := nil;
      LFormatEtc.dwAspect := DVASPECT_CONTENT;
      LFormatEtc.lindex := -1;
      LFormatEtc.tymed := TYMED_HGLOBAL;
      
      if LDropTarget.DataObject.GetData(LFormatEtc, LMedium) = S_OK then
      begin
        try
          LFileGroupDesc := GlobalLock(LMedium.hGlobal);
          if Assigned(LFileGroupDesc) and (LFileGroupDesc.cItems > 0) then
          begin
            try
              LFileName := string(LFileGroupDesc.fgd[0].cFileName);
              if Pos('.msg', LowerCase(LFileName)) > 0 then
                LFileName := Copy(LFileName, 1, Pos('.msg', LowerCase(LFileName)) - 1);
              
              Memo1.Lines.Add(Format('Mail dropped at %s', [TimeToStr(Now)]));
              Memo1.Lines.Add(Format('Subject: %s', [LFileName]));
              Memo1.Lines.Add('From: [Not available]');
              Memo1.Lines.Add('---');
            finally
              GlobalUnlock(LMedium.hGlobal);
            end;
          end;
        finally
          ReleaseStgMedium(LMedium);
        end;
      end;
    end;
  except
    on E: Exception do
      Memo1.Lines.Add(Format('Error accessing Outlook: %s', [E.Message]));
  end;
end;

end.

