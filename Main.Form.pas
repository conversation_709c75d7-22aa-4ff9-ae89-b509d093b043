unit Main.Form;

interface

uses
  Winapi.Windows, Winapi.Messages,
  System.SysUtils, System.Variants, System.Classes,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Vcl.Graphics, Vcl.StdCtrls,
  OutlookDragDrop;

type
  TFormMain = class(TForm)
    Memo1: TMemo;
    Panel1: TPanel;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
  private
    FDragDropManager: TOutlookDragDropManager;
    procedure HandleOutlookDrop(Sender: TObject; const AMailInfo: TOutlookMailInfo);
  public
    { Public declarations }
  end;

var
  FormMain: TFormMain;

implementation

{$R *.dfm}

{ TFormMain }

procedure TFormMain.FormCreate(Sender: TObject);
begin
  FDragDropManager := TOutlookDragDropManager.Create(Panel1);
  FDragDropManager.OnDropMail := HandleOutlookDrop;
  FDragDropManager.Initialize;
end;

procedure TFormMain.FormDestroy(Sender: TObject);
begin
  FreeAndNil(FDragDropManager);
end;

procedure TFormMain.HandleOutlookDrop(Sender: TObject; const AMailInfo: TOutlookMailInfo);
var
  i: Integer;
begin
  try
    Memo1.Lines.Add(Format('Mail dropped at %s', [TimeToStr(Now)]));
    Memo1.Lines.Add(Format('From: %s', [AMailInfo.SenderName]));
    Memo1.Lines.Add(Format('Email: %s', [AMailInfo.SenderEmail]));
    Memo1.Lines.Add(Format('Subject: %s', [AMailInfo.Subject]));

    if AMailInfo.HasFullInfo then
    begin
      Memo1.Lines.Add(Format('Received: %s', [DateTimeToStr(AMailInfo.ReceivedTime)]));
      Memo1.Lines.Add(Format('Size: %d bytes', [AMailInfo.Size]));

      // Recipients
      if Length(AMailInfo.Recipients) > 0 then
      begin
        Memo1.Lines.Add('Recipients:');
        for i := 0 to High(AMailInfo.Recipients) do
          Memo1.Lines.Add(Format('  - %s', [AMailInfo.Recipients[i]]));
      end;

      // Attachments
      if Length(AMailInfo.Attachments) > 0 then
      begin
        Memo1.Lines.Add('Attachments:');
        for i := 0 to High(AMailInfo.Attachments) do
          Memo1.Lines.Add(Format('  - %s', [AMailInfo.Attachments[i]]));
      end;

      // Body preview
      if AMailInfo.BodyPreview <> '' then
      begin
        Memo1.Lines.Add('Body preview:');
        Memo1.Lines.Add(AMailInfo.BodyPreview);
      end;
    end
    else
    begin
      Memo1.Lines.Add('(Limited information available)');
    end;

    Memo1.Lines.Add('---');
  except
    on E: Exception do
      Memo1.Lines.Add(Format('Error processing dropped mail: %s', [E.Message]));
  end;
end;

end.

