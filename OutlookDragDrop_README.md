# OutlookDragDrop Unit

A clean, easy-to-use Delphi unit for handling Outlook email drag and drop operations.

## Overview

The `OutlookDragDrop.pas` unit provides a simple interface for enabling Outlook email drag and drop functionality in Delphi applications. It isolates all the complex COM/OLE interactions and provides a clean, event-based interface.

## Features

- **Simple API**: Just create a manager, assign an event handler, and call Initialize
- **Automatic cleanup**: OLE initialization/cleanup handled automatically
- **Rich mail information**: Extracts sender, subject, recipients, attachments, body preview, etc.
- **Fallback support**: Works even when full Outlook integration isn't available
- **Error handling**: Robust error handling with graceful degradation
- **Memory management**: Proper COM object lifecycle management

## Quick Start

```pascal
uses OutlookDragDrop;

// In your form class
private
  FDragDropManager: TOutlookDragDropManager;
  procedure HandleMailDrop(Sender: TObject; const AMailInfo: TOutlookMailInfo);

// In FormCreate
procedure TMyForm.FormCreate(Sender: TObject);
begin
  FDragDropManager := TOutlookDragDropManager.Create(Panel1); // Any TWinControl
  FDragDropManager.OnDropMail := HandleMailDrop;
  FDragDropManager.Initialize;
end;

// In FormDestroy
procedure TMyForm.FormDestroy(Sender: TObject);
begin
  FreeAndNil(FDragDropManager); // Automatic cleanup
end;

// Handle dropped emails
procedure TMyForm.HandleMailDrop(Sender: TObject; const AMailInfo: TOutlookMailInfo);
begin
  ShowMessage(Format('Received: %s from %s', [AMailInfo.Subject, AMailInfo.SenderName]));
end;
```

## TOutlookMailInfo Record

The `TOutlookMailInfo` record contains all extracted email information:

```pascal
TOutlookMailInfo = record
  SenderName: string;        // Sender's display name
  SenderEmail: string;       // Sender's email address
  Subject: string;           // Email subject
  ReceivedTime: TDateTime;   // When email was received
  Size: Integer;             // Email size in bytes
  Recipients: TArray<string>; // List of recipients
  Attachments: TArray<string>; // List of attachments with sizes
  BodyPreview: string;       // First 200 characters of body
  HasFullInfo: Boolean;      // True if full info available, False if limited
end;
```

## Classes

### TOutlookDragDropManager

The main class for managing Outlook drag and drop functionality.

**Properties:**
- `OnDropMail: TOutlookDropEvent` - Event fired when email is dropped
- `Initialized: Boolean` - Whether drag and drop is currently active

**Methods:**
- `Create(AControl: TWinControl)` - Create manager for specified control
- `Initialize` - Enable drag and drop (calls OleInitialize)
- `Finalize` - Disable drag and drop (calls OleUninitialize)

### TOutlookDropTarget

Low-level drop target implementation (usually not used directly).

## Error Handling

The unit includes comprehensive error handling:

- **Initialization errors**: Throws exception if OLE initialization fails
- **Runtime errors**: Silently handled, event still fires with available data
- **Cleanup errors**: Ignored during finalization to prevent shutdown issues

## Requirements

- Windows with OLE support
- Microsoft Outlook (for full functionality)
- Delphi VCL application

## Implementation Notes

- Uses Unicode clipboard formats (`FileGroupDescriptorW`)
- Attempts direct Outlook COM access first, falls back to data object parsing
- Automatically manages OLE initialization/cleanup
- Thread-safe for single-threaded apartment (STA) applications

## Example Applications

See `OutlookDragDropExample.pas` for a minimal working example.

The main application (`Main.Form.pas`) shows a more comprehensive implementation with detailed information display.

## Migration from Legacy Code

If you have existing drag and drop code, migration is straightforward:

**Before:**
```pascal
// Complex IDropTarget implementation
// Manual OLE initialization
// Manual COM object management
// Complex data extraction logic
```

**After:**
```pascal
FDragDropManager := TOutlookDragDropManager.Create(Panel1);
FDragDropManager.OnDropMail := HandleMailDrop;
FDragDropManager.Initialize;
```

## License

This unit follows the same license as the containing project.
