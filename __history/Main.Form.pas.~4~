﻿unit Main.Form;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Winapi.ActiveX, Winapi.ShlObj, System.Win.ComObj,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Vcl.StdCtrls;

type
  TOutlookDropTarget = class(TInterfacedObject, IDropTarget)
  private
    FOwner: TPanel;
    FOnDropFiles: TNotifyEvent;
    FDataObject: IDataObject; // Store data object for extraction
  public
    constructor Create(AOwner: TPanel);
    function DragEnter(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
      stdcall;
    function DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;
    function DragLeave: HResult; stdcall;
    function Drop(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
      stdcall;
    property OnDropFiles: TNotifyEvent read FOnDropFiles write FOnDropFiles;
    property DataObject: IDataObject read FDataObject;
  end;

  TFormMain = class(TForm)
    Memo1: TMemo;
    Panel1: TPanel;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
  private
    FDropTarget: TOutlookDropTarget;
    procedure HandleOutlookDrop(Sender: TObject);
    function ExtractSenderFromMsg(const AContent: AnsiString): string;
    function ExtractSubjectFromMsg(const AContent: AnsiString): string;
  public
    { Public declarations }
  end;

var
  FormMain: TFormMain;

implementation

{$R *.dfm}

const
  C_OUTLOOK_MSG_FORMAT = 'FileGroupDescriptor';
  C_OUTLOOK_CONTENT_FORMAT = 'FileContents';

  { TOutlookDropTarget }

constructor TOutlookDropTarget.Create(AOwner: TPanel);
begin
  inherited Create;
  FOwner := AOwner;
end;

function TOutlookDropTarget.DragEnter(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect:
  Longint): HResult;
var
  LFormatEtc: TFormatEtc;
begin
  // Check for Outlook message format
  LFormatEtc.cfFormat := RegisterClipboardFormat(C_OUTLOOK_MSG_FORMAT);
  LFormatEtc.ptd := nil;
  LFormatEtc.dwAspect := DVASPECT_CONTENT;
  LFormatEtc.lindex := -1;
  LFormatEtc.tymed := TYMED_HGLOBAL;

  if dataObj.QueryGetData(LFormatEtc) = S_OK then
    dwEffect := DROPEFFECT_COPY
  else
    dwEffect := DROPEFFECT_NONE;

  Result := S_OK;
end;

function TOutlookDropTarget.DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
begin
  dwEffect := DROPEFFECT_COPY;
  Result := S_OK;
end;

function TOutlookDropTarget.DragLeave: HResult;
begin
  Result := S_OK;
end;

function TOutlookDropTarget.Drop(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint):
  HResult;
begin
  dwEffect := DROPEFFECT_COPY;
  FDataObject := dataObj; // Store for extraction
  if Assigned(FOnDropFiles) then
    FOnDropFiles(Self);
  Result := S_OK;
end;

{ TFormMain }

procedure TFormMain.FormCreate(Sender: TObject);
begin
  OleInitialize(nil);
  FDropTarget := TOutlookDropTarget.Create(Panel1);
  FDropTarget.OnDropFiles := HandleOutlookDrop;
  RegisterDragDrop(Panel1.Handle, FDropTarget);
end;

procedure TFormMain.FormDestroy(Sender: TObject);
begin
  if Assigned(FDropTarget) then
  begin
    RevokeDragDrop(Panel1.Handle);
    FDropTarget := nil;
  end;
  OleUninitialize;
end;

procedure TFormMain.HandleOutlookDrop(Sender: TObject);
var
  LDropTarget: TOutlookDropTarget;
  LFormatEtc: TFormatEtc;
  LMedium: TStgMedium;
  LFileGroupDesc: PFileGroupDescriptor;
  LFileDesc: TFileDescriptor;
  LContentFormat: TFormatEtc;
  LContentMedium: TStgMedium;
  LStream: IStream;
  LBuffer: array[0..1023] of AnsiChar;
  LBytesRead: Longint;
  LContent: AnsiString;
  LSender, LSubject: string;
begin
  LDropTarget := Sender as TOutlookDropTarget;

  // Get FileGroupDescriptor
  LFormatEtc.cfFormat := RegisterClipboardFormat(C_OUTLOOK_MSG_FORMAT);
  LFormatEtc.ptd := nil;
  LFormatEtc.dwAspect := DVASPECT_CONTENT;
  LFormatEtc.lindex := -1;
  LFormatEtc.tymed := TYMED_HGLOBAL;

  if LDropTarget.DataObject.GetData(LFormatEtc, LMedium) = S_OK then
  begin
    try
      LFileGroupDesc := GlobalLock(LMedium.hGlobal);
      if Assigned(LFileGroupDesc) then
      begin
        try
          if LFileGroupDesc.cItems > 0 then
          begin
            LFileDesc := LFileGroupDesc.fgd[0]; // First file

            // Get file content
            LContentFormat.cfFormat := RegisterClipboardFormat(C_OUTLOOK_CONTENT_FORMAT);
            LContentFormat.ptd := nil;
            LContentFormat.dwAspect := DVASPECT_CONTENT;
            LContentFormat.lindex := 0;
            LContentFormat.tymed := TYMED_ISTREAM;

            Memo1.Lines.Add(Format('Mail dropped at %s', [TimeToStr(Now)]));
            if LDropTarget.DataObject.GetData(LContentFormat, LContentMedium) = S_OK then
            begin
              try
                LStream := IStream(LContentMedium.stm);
                LContent := '';

                // Read stream content
                repeat
                  LBytesRead := 0;
                  LStream.read(@LBuffer[0], SizeOf(LBuffer), @LBytesRead);
                  if LBytesRead > 0 then
                    LContent := LContent + Copy(LBuffer, 1, LBytesRead);
                until LBytesRead = 0;

                // Extract sender and subject from MSG content
                LSender := ExtractSenderFromMsg(LContent);
                LSubject := ExtractSubjectFromMsg(LContent);

                Memo1.Lines.Add(Format('From: %s', [LSender]));
                Memo1.Lines.Add(Format('Subject: %s', [LSubject]));
                Memo1.Lines.Add('---');
              finally
                ReleaseStgMedium(LContentMedium);
              end;
            end;
          end;
        finally
          GlobalUnlock(LMedium.hGlobal);
        end;
      end;
    finally
      ReleaseStgMedium(LMedium);
    end;
  end;
end;

function TFormMain.ExtractSenderFromMsg(const AContent: AnsiString): string;
var
  LPos: Integer;
begin
  // Simple extraction - look for sender in MSG format
  LPos := Pos(#$0C#$00#$1F#$00, AContent); // MAPI property for sender
  if LPos > 0 then
    Result := 'Extracted Sender' // Implement proper MSG parsing
  else
    Result := 'Unknown Sender';
end;

function TFormMain.ExtractSubjectFromMsg(const AContent: AnsiString): string;
var
  LPos: Integer;
begin
  // Simple extraction - look for subject in MSG format
  LPos := Pos(#$37#$00#$1F#$00, AContent); // MAPI property for subject
  if LPos > 0 then
    Result := 'Extracted Subject' // Implement proper MSG parsing
  else
    Result := 'No Subject';
end;

end.

