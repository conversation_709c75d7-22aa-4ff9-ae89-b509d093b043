﻿unit Main.Form;

interface

uses
  Winapi.Windows, Winapi.Messages, Winapi.ActiveX, Winapi.ShlObj, Winapi.ShellAPI,
  System.SysUtils, System.Variants, System.Classes, System.Win.ComObj,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Vcl.Graphics, Vcl.StdCtrls;

type
  TOutlookDropTarget = class(TInterfacedObject, IDropTarget)
  private
    FOwner: TPanel;
    FOnDropFiles: TNotifyEvent;
    FDataObject: IDataObject; // Store data object for extraction
  public
    constructor Create(AOwner: TPanel);
    function DragEnter(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
      stdcall;
    function DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;
    function DragLeave: HResult; stdcall;
    function Drop(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
      stdcall;
    property OnDropFiles: TNotifyEvent read FOnDropFiles write FOnDropFiles;
    property DataObject: IDataObject read FDataObject;
  end;

  TFormMain = class(TForm)
    Memo1: TMemo;
    Panel1: TPanel;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
  private
    FDropTarget: TOutlookDropTarget;
    procedure HandleOutlookDrop(Sender: TObject);
    function SaveDroppedMsgToFile(const ADataObject: IDataObject): string;
    procedure ParseMsgFile(const AFileName: string);
    function GetTempDir: string;
    function ExtractSenderFromMsg(const AContent: AnsiString): string;
    function ExtractSubjectFromMsg(const AContent: AnsiString): string;
  public
    { Public declarations }
  end;

var
  FormMain: TFormMain;

implementation

{$R *.dfm}

const
  C_OUTLOOK_MSG_FORMAT = 'FileGroupDescriptorW'; // Use Unicode version
  C_OUTLOOK_CONTENT_FORMAT = 'FileContents';

  { TOutlookDropTarget }

constructor TOutlookDropTarget.Create(AOwner: TPanel);
begin
  inherited Create;
  FOwner := AOwner;
end;

function TOutlookDropTarget.DragEnter(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect:
  Longint): HResult;
var
  LFormatEtc: TFormatEtc;
begin
  // Check for Outlook message format
  LFormatEtc.cfFormat := RegisterClipboardFormat(C_OUTLOOK_MSG_FORMAT);
  LFormatEtc.ptd := nil;
  LFormatEtc.dwAspect := DVASPECT_CONTENT;
  LFormatEtc.lindex := -1;
  LFormatEtc.tymed := TYMED_HGLOBAL;

  if dataObj.QueryGetData(LFormatEtc) = S_OK then
    dwEffect := DROPEFFECT_COPY
  else
    dwEffect := DROPEFFECT_NONE;

  Result := S_OK;
end;

function TOutlookDropTarget.DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
begin
  dwEffect := DROPEFFECT_COPY;
  Result := S_OK;
end;

function TOutlookDropTarget.DragLeave: HResult;
begin
  Result := S_OK;
end;

function TOutlookDropTarget.Drop(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint):
  HResult;
begin
  dwEffect := DROPEFFECT_COPY;
  FDataObject := dataObj; // Store for extraction
  if Assigned(FOnDropFiles) then
    FOnDropFiles(Self);
  Result := S_OK;
end;

{ TFormMain }

procedure TFormMain.FormCreate(Sender: TObject);
begin
  OleInitialize(nil);
  FDropTarget := TOutlookDropTarget.Create(Panel1);
  FDropTarget.OnDropFiles := HandleOutlookDrop;
  RegisterDragDrop(Panel1.Handle, FDropTarget);
end;

procedure TFormMain.FormDestroy(Sender: TObject);
begin
  if Assigned(FDropTarget) then
  begin
    RevokeDragDrop(Panel1.Handle);
    FDropTarget := nil;
  end;
  OleUninitialize;
end;

procedure TFormMain.HandleOutlookDrop(Sender: TObject);
var
  LDropTarget: TOutlookDropTarget;
  LFormatEtc: TFormatEtc;
  LMedium: TStgMedium;
  LFileGroupDesc: PFileGroupDescriptorW;
  LFileName: string;
  LOutlookApp: OleVariant;
  LSelection: OleVariant;
  LMailItem: OleVariant;
  i: Integer;
  LBody: string;
begin
  LDropTarget := Sender as TOutlookDropTarget;
  
  try
    // Try to get Outlook application
    try
      LOutlookApp := GetActiveOleObject('Outlook.Application');
    except
      LOutlookApp := CreateOleObject('Outlook.Application');
    end;
    
    // Get current selection (the dragged item)
    LSelection := LOutlookApp.ActiveExplorer.Selection;
    if LSelection.Count > 0 then
    begin
      LMailItem := LSelection.Item(1);
      
      Memo1.Lines.Add(Format('Mail dropped at %s', [TimeToStr(Now)]));
      Memo1.Lines.Add(Format('From: %s', [string(LMailItem.SenderName)]));
      Memo1.Lines.Add(Format('Email: %s', [string(LMailItem.SenderEmailAddress)]));
      Memo1.Lines.Add(Format('Subject: %s', [string(LMailItem.Subject)]));
      Memo1.Lines.Add(Format('Received: %s', [string(LMailItem.ReceivedTime)]));
      Memo1.Lines.Add(Format('Size: %d bytes', [Integer(LMailItem.Size)]));
      
      // Recipients
      if LMailItem.Recipients.Count > 0 then
      begin
        Memo1.Lines.Add('Recipients:');
        for i := 1 to LMailItem.Recipients.Count do
          Memo1.Lines.Add(Format('  - %s (%s)', [string(LMailItem.Recipients.Item(i).Name), string(LMailItem.Recipients.Item(i).Address)]));
      end;
      
      // Attachments
      if LMailItem.Attachments.Count > 0 then
      begin
        Memo1.Lines.Add('Attachments:');
        for i := 1 to LMailItem.Attachments.Count do
          Memo1.Lines.Add(Format('  - %s (%d bytes)', [string(LMailItem.Attachments.Item(i).FileName), Integer(LMailItem.Attachments.Item(i).Size)]));
      end;
      
      // Body preview (first 200 characters)
      LBody := string(LMailItem.Body);
      if Length(LBody) > 200 then
        LBody := Copy(LBody, 1, 200) + '...';
      Memo1.Lines.Add('Body preview:');
      Memo1.Lines.Add(LBody);
      Memo1.Lines.Add('---');
    end
    else
    begin
      // Fallback to filename extraction
      LFormatEtc.cfFormat := RegisterClipboardFormat(C_OUTLOOK_MSG_FORMAT);
      LFormatEtc.ptd := nil;
      LFormatEtc.dwAspect := DVASPECT_CONTENT;
      LFormatEtc.lindex := -1;
      LFormatEtc.tymed := TYMED_HGLOBAL;
      
      if LDropTarget.DataObject.GetData(LFormatEtc, LMedium) = S_OK then
      begin
        try
          LFileGroupDesc := GlobalLock(LMedium.hGlobal);
          if Assigned(LFileGroupDesc) and (LFileGroupDesc.cItems > 0) then
          begin
            try
              LFileName := string(LFileGroupDesc.fgd[0].cFileName);
              if Pos('.msg', LowerCase(LFileName)) > 0 then
                LFileName := Copy(LFileName, 1, Pos('.msg', LowerCase(LFileName)) - 1);
              
              Memo1.Lines.Add(Format('Mail dropped at %s', [TimeToStr(Now)]));
              Memo1.Lines.Add(Format('Subject: %s', [LFileName]));
              Memo1.Lines.Add('From: [Not available]');
              Memo1.Lines.Add('---');
            finally
              GlobalUnlock(LMedium.hGlobal);
            end;
          end;
        finally
          ReleaseStgMedium(LMedium);
        end;
      end;
    end;
  except
    on E: Exception do
      Memo1.Lines.Add(Format('Error accessing Outlook: %s', [E.Message]));
  end;
end;

function TFormMain.SaveDroppedMsgToFile(const ADataObject: IDataObject): string;
var
  LContentFormat: TFormatEtc;
  LContentMedium: TStgMedium;
  LStream: IStream;
  LFileStream: TFileStream;
  LBuffer: array[0..4095] of Byte;
  LBytesRead: Longint;
  LGetDataResult: HResult;
  LTempDir: string;
  LTempFileName: string;
  LDropFiles: HDROP;
  LFileCount: Integer;
  LFileNameBuffer: array[0..MAX_PATH] of Char;
begin
  Result := '';
  
  // First try standard file drop format
  LContentFormat.cfFormat := CF_HDROP;
  LContentFormat.ptd := nil;
  LContentFormat.dwAspect := DVASPECT_CONTENT;
  LContentFormat.lindex := -1;
  LContentFormat.tymed := TYMED_HGLOBAL;
  
  LGetDataResult := ADataObject.GetData(LContentFormat, LContentMedium);
  if LGetDataResult = S_OK then
  begin
    try
      LDropFiles := HDROP(LContentMedium.hGlobal);
      LFileCount := DragQueryFile(LDropFiles, $FFFFFFFF, nil, 0);
      if LFileCount > 0 then
      begin
        DragQueryFile(LDropFiles, 0, LFileNameBuffer, MAX_PATH);
        Result := string(LFileNameBuffer);
        Exit;
      end;
    finally
      ReleaseStgMedium(LContentMedium);
    end;
  end;

  // If that fails, try FileContents with delay and retry
  LContentFormat.cfFormat := RegisterClipboardFormat('FileContents');
  LContentFormat.ptd := nil;
  LContentFormat.dwAspect := DVASPECT_CONTENT;
  LContentFormat.lindex := 0;
  LContentFormat.tymed := TYMED_ISTREAM;
  
  // Try multiple times with delays (Outlook sometimes needs time)
  for var i := 1 to 3 do
  begin
    Sleep(50 * i); // Increasing delay
    LGetDataResult := ADataObject.GetData(LContentFormat, LContentMedium);
    if LGetDataResult = S_OK then
      Break;
  end;
  
  if LGetDataResult = S_OK then
  begin
    try
      LTempDir := GetTempDir;
      LTempFileName := LTempDir + 'outlook_drop_' + FormatDateTime('yyyymmdd_hhnnss', Now) + '.msg';
      
      LFileStream := TFileStream.Create(LTempFileName, fmCreate);
      try
        LStream := IStream(LContentMedium.stm);
        repeat
          LBytesRead := 0;
          LStream.Read(@LBuffer[0], SizeOf(LBuffer), @LBytesRead);
          if LBytesRead > 0 then
            LFileStream.WriteBuffer(LBuffer, LBytesRead);
        until LBytesRead = 0;
        
        Result := LTempFileName;
      finally
        LFileStream.Free;
      end;
    finally
      ReleaseStgMedium(LContentMedium);
    end;
  end;
end;

function TFormMain.ExtractSenderFromMsg(const AContent: AnsiString): string;
var
  LPos: Integer;
begin
  // Simple extraction - look for sender in MSG format
  LPos := Pos(#$0C#$00#$1F#$00, AContent); // MAPI property for sender
  if LPos > 0 then
    Result := 'Extracted Sender' // Implement proper MSG parsing
  else
    Result := 'Unknown Sender';
end;

function TFormMain.ExtractSubjectFromMsg(const AContent: AnsiString): string;
var
  LPos: Integer;
begin
  // Simple extraction - look for subject in MSG format
  LPos := Pos(#$37#$00#$1F#$00, AContent); // MAPI property for subject
  if LPos > 0 then
    Result := 'Extracted Subject' // Implement proper MSG parsing
  else
    Result := 'No Subject';
end;

function TFormMain.GetTempDir: string;
var
  LTempPath: array[0..MAX_PATH] of Char;
begin
  GetTempPath(MAX_PATH, LTempPath);
  Result := IncludeTrailingPathDelimiter(string(LTempPath));
end;

procedure TFormMain.ParseMsgFile(const AFileName: string);
var
  LFileStream: TFileStream;
  LContent: AnsiString;
  LSender, LSubject: string;
begin
  if not FileExists(AFileName) then
  begin
    Memo1.Lines.Add('Error: MSG file not found');
    Exit;
  end;

  try
    LFileStream := TFileStream.Create(AFileName, fmOpenRead);
    try
      SetLength(LContent, LFileStream.Size);
      LFileStream.ReadBuffer(LContent[1], LFileStream.Size);

      LSender := ExtractSenderFromMsg(LContent);
      LSubject := ExtractSubjectFromMsg(LContent);

      Memo1.Lines.Add(Format('Mail dropped at %s', [TimeToStr(Now)]));
      Memo1.Lines.Add(Format('File size: %d bytes', [LFileStream.Size]));
      Memo1.Lines.Add(Format('From: %s', [LSender]));
      Memo1.Lines.Add(Format('Subject: %s', [LSubject]));
      Memo1.Lines.Add('---');
    finally
      LFileStream.Free;
    end;
  except
    on E: Exception do
      Memo1.Lines.Add(Format('Error reading MSG file: %s', [E.Message]));
  end;
end;

end.

