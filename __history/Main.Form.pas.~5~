﻿unit Main.Form;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes, Vcl.Graphics,
  Winapi.ActiveX, Winapi.ShlObj, System.Win.ComObj,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Vcl.StdCtrls;

type
  TOutlookDropTarget = class(TInterfacedObject, IDropTarget)
  private
    FOwner: TPanel;
    FOnDropFiles: TNotifyEvent;
    FDataObject: IDataObject; // Store data object for extraction
  public
    constructor Create(AOwner: TPanel);
    function DragEnter(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
      stdcall;
    function DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;
    function DragLeave: HResult; stdcall;
    function Drop(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
      stdcall;
    property OnDropFiles: TNotifyEvent read FOnDropFiles write FOnDropFiles;
    property DataObject: IDataObject read FDataObject;
  end;

  TFormMain = class(TForm)
    Memo1: TMemo;
    Panel1: TPanel;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
  private
    FDropTarget: TOutlookDropTarget;
    procedure HandleOutlookDrop(Sender: TObject);
    function SaveDroppedMsgToFile(const ADataObject: IDataObject): string;
    procedure ParseMsgFile(const AFileName: string);
    function GetTempDir: string;
    function ExtractSenderFromMsg(const AContent: AnsiString): string;
    function ExtractSubjectFromMsg(const AContent: AnsiString): string;
  public
    { Public declarations }
  end;

var
  FormMain: TFormMain;

implementation

{$R *.dfm}

const
  C_OUTLOOK_MSG_FORMAT = 'FileGroupDescriptorW';  // Use Unicode version
  C_OUTLOOK_CONTENT_FORMAT = 'FileContents';

  { TOutlookDropTarget }

constructor TOutlookDropTarget.Create(AOwner: TPanel);
begin
  inherited Create;
  FOwner := AOwner;
end;

function TOutlookDropTarget.DragEnter(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect:
  Longint): HResult;
var
  LFormatEtc: TFormatEtc;
begin
  // Check for Outlook message format
  LFormatEtc.cfFormat := RegisterClipboardFormat(C_OUTLOOK_MSG_FORMAT);
  LFormatEtc.ptd := nil;
  LFormatEtc.dwAspect := DVASPECT_CONTENT;
  LFormatEtc.lindex := -1;
  LFormatEtc.tymed := TYMED_HGLOBAL;

  if dataObj.QueryGetData(LFormatEtc) = S_OK then
    dwEffect := DROPEFFECT_COPY
  else
    dwEffect := DROPEFFECT_NONE;

  Result := S_OK;
end;

function TOutlookDropTarget.DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
begin
  dwEffect := DROPEFFECT_COPY;
  Result := S_OK;
end;

function TOutlookDropTarget.DragLeave: HResult;
begin
  Result := S_OK;
end;

function TOutlookDropTarget.Drop(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint):
  HResult;
begin
  dwEffect := DROPEFFECT_COPY;
  FDataObject := dataObj; // Store for extraction
  if Assigned(FOnDropFiles) then
    FOnDropFiles(Self);
  Result := S_OK;
end;

{ TFormMain }

procedure TFormMain.FormCreate(Sender: TObject);
begin
  OleInitialize(nil);
  FDropTarget := TOutlookDropTarget.Create(Panel1);
  FDropTarget.OnDropFiles := HandleOutlookDrop;
  RegisterDragDrop(Panel1.Handle, FDropTarget);
end;

procedure TFormMain.FormDestroy(Sender: TObject);
begin
  if Assigned(FDropTarget) then
  begin
    RevokeDragDrop(Panel1.Handle);
    FDropTarget := nil;
  end;
  OleUninitialize;
end;

procedure TFormMain.HandleOutlookDrop(Sender: TObject);
var
  LDropTarget: TOutlookDropTarget;
  LTempFile: string;
begin
  LDropTarget := Sender as TOutlookDropTarget;
  
  // Save dropped content to temporary MSG file
  LTempFile := SaveDroppedMsgToFile(LDropTarget.DataObject);
  if LTempFile <> '' then
  begin
    try
      ParseMsgFile(LTempFile);
    finally
      DeleteFile(LTempFile);
    end;
  end
  else
    Memo1.Lines.Add('Failed to save dropped message to file');
end;

function TFormMain.SaveDroppedMsgToFile(const ADataObject: IDataObject): string;
var
  LFormatEtc: TFormatEtc;
  LMedium: TStgMedium;
  LContentFormat: TFormatEtc;
  LContentMedium: TStgMedium;
  LStream: IStream;
  LFileStream: TFileStream;
  LBuffer: array[0..4095] of Byte;
  LBytesRead: Longint;
begin
  Result := '';
  
  // Get FileContents and save to temp file
  LContentFormat.cfFormat := RegisterClipboardFormat('FileContents');
  LContentFormat.ptd := nil;
  LContentFormat.dwAspect := DVASPECT_CONTENT;
  LContentFormat.lindex := 0;
  LContentFormat.tymed := TYMED_ISTREAM;
  
  if ADataObject.GetData(LContentFormat, LContentMedium) = S_OK then
  begin
    try
      Result := GetTempDir + 'outlook_drop_' + FormatDateTime('yyyymmdd_hhnnss', Now) + '.msg';
      LFileStream := TFileStream.Create(Result, fmCreate);
      try
        LStream := IStream(LContentMedium.stm);
        repeat
          LBytesRead := 0;
          LStream.Read(@LBuffer[0], SizeOf(LBuffer), @LBytesRead);
          if LBytesRead > 0 then
            LFileStream.WriteBuffer(LBuffer, LBytesRead);
        until LBytesRead = 0;
      finally
        LFileStream.Free;
      end;
    finally
      ReleaseStgMedium(LContentMedium);
    end;
  end;
end;

function TFormMain.ExtractSenderFromMsg(const AContent: AnsiString): string;
var
  LPos: Integer;
begin
  // Simple extraction - look for sender in MSG format
  LPos := Pos(#$0C#$00#$1F#$00, AContent); // MAPI property for sender
  if LPos > 0 then
    Result := 'Extracted Sender' // Implement proper MSG parsing
  else
    Result := 'Unknown Sender';
end;

function TFormMain.ExtractSubjectFromMsg(const AContent: AnsiString): string;
var
  LPos: Integer;
begin
  // Simple extraction - look for subject in MSG format
  LPos := Pos(#$37#$00#$1F#$00, AContent); // MAPI property for subject
  if LPos > 0 then
    Result := 'Extracted Subject' // Implement proper MSG parsing
  else
    Result := 'No Subject';
end;

function TFormMain.GetTempDir: string;
var
  LTempPath: array[0..MAX_PATH] of Char;
begin
  GetTempPath(MAX_PATH, LTempPath);
  Result := IncludeTrailingPathDelimiter(string(LTempPath));
end;

procedure TFormMain.ParseMsgFile(const AFileName: string);
var
  LFileStream: TFileStream;
  LContent: AnsiString;
  LSender, LSubject: string;
begin
  if not FileExists(AFileName) then
  begin
    Memo1.Lines.Add('Error: MSG file not found');
    Exit;
  end;
  
  try
    LFileStream := TFileStream.Create(AFileName, fmOpenRead);
    try
      SetLength(LContent, LFileStream.Size);
      LFileStream.ReadBuffer(LContent[1], LFileStream.Size);
      
      LSender := ExtractSenderFromMsg(LContent);
      LSubject := ExtractSubjectFromMsg(LContent);
      
      Memo1.Lines.Add(Format('Mail dropped at %s', [TimeToStr(Now)]));
      Memo1.Lines.Add(Format('File size: %d bytes', [LFileStream.Size]));
      Memo1.Lines.Add(Format('From: %s', [LSender]));
      Memo1.Lines.Add(Format('Subject: %s', [LSubject]));
      Memo1.Lines.Add('---');
    finally
      LFileStream.Free;
    end;
  except
    on E: Exception do
      Memo1.Lines.Add(Format('Error reading MSG file: %s', [E.Message]));
  end;
end;

end.

