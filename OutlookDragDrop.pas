unit OutlookDragDrop;

/// <summary>
/// Outlook Drag and Drop Support Unit
/// 
/// This unit provides a clean interface for handling Outlook mail item drag and drop
/// operations. It isolates all the COM/OLE complexity and provides a simple event-based
/// interface for consuming applications.
/// 
/// Usage:
///   1. Create a TOutlookDragDropManager instance
///   2. Assign an event handler to OnDropMail
///   3. Call Initialize to enable drag and drop
///   4. Call Finalize when done (or let destructor handle it)
/// </summary>

interface

uses
  Winapi.Windows, Winapi.Messages, Winapi.ActiveX, Winapi.ShlObj, Winapi.ShellAPI,
  System.SysUtils, System.Variants, System.Classes, System.Win.ComObj,
  Vcl.Controls;

type
  /// <summary>
  /// Record containing information about a dropped Outlook mail item
  /// </summary>
  TOutlookMailInfo = record
    SenderName: string;
    SenderEmail: string;
    Subject: string;
    ReceivedTime: TDateTime;
    Size: Integer;
    Recipients: TArray<string>;
    Attachments: TArray<string>;
    BodyPreview: string;
    HasFullInfo: Boolean;
  end;

  /// <summary>
  /// Event handler for Outlook mail drop events
  /// </summary>
  TOutlookDropEvent = procedure(Sender: TObject; const AMailInfo: TOutlookMailInfo) of object;

  /// <summary>
  /// Drop target implementation for Outlook mail items
  /// </summary>
  TOutlookDropTarget = class(TInterfacedObject, IDropTarget)
  private
    FOwner: TWinControl;
    FOnDropMail: TOutlookDropEvent;
    FDataObject: IDataObject;
    function ExtractMailInfoFromOutlook: TOutlookMailInfo;
    function ExtractMailInfoFromDataObject: TOutlookMailInfo;
  public
    constructor Create(AOwner: TWinControl);
    function DragEnter(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;
    function DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;
    function DragLeave: HResult; stdcall;
    function Drop(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult; stdcall;
    property OnDropMail: TOutlookDropEvent read FOnDropMail write FOnDropMail;
  end;

  /// <summary>
  /// Manager class for Outlook drag and drop functionality
  /// Provides a high-level interface for enabling/disabling Outlook drag and drop
  /// </summary>
  TOutlookDragDropManager = class
  private
    FDropTarget: TOutlookDropTarget;
    FControl: TWinControl;
    FOnDropMail: TOutlookDropEvent;
    FInitialized: Boolean;
    procedure SetOnDropMail(const AValue: TOutlookDropEvent);
  public
    constructor Create(AControl: TWinControl);
    destructor Destroy; override;
    procedure Initialize;
    procedure Finalize;
    property OnDropMail: TOutlookDropEvent read FOnDropMail write SetOnDropMail;
    property Initialized: Boolean read FInitialized;
  end;

implementation

const
  C_OUTLOOK_MSG_FORMAT = 'FileGroupDescriptorW';
  C_OUTLOOK_CONTENT_FORMAT = 'FileContents';
  C_BODY_PREVIEW_LENGTH = 200;

{ TOutlookDropTarget }

constructor TOutlookDropTarget.Create(AOwner: TWinControl);
begin
  inherited Create;
  FOwner := AOwner;
end;

function TOutlookDropTarget.DragEnter(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
var
  LFormatEtc: TFormatEtc;
begin
  // Check for Outlook message format
  LFormatEtc.cfFormat := RegisterClipboardFormat(C_OUTLOOK_MSG_FORMAT);
  LFormatEtc.ptd := nil;
  LFormatEtc.dwAspect := DVASPECT_CONTENT;
  LFormatEtc.lindex := -1;
  LFormatEtc.tymed := TYMED_HGLOBAL;

  if dataObj.QueryGetData(LFormatEtc) = S_OK then
    dwEffect := DROPEFFECT_COPY
  else
    dwEffect := DROPEFFECT_NONE;

  Result := S_OK;
end;

function TOutlookDropTarget.DragOver(grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
begin
  dwEffect := DROPEFFECT_COPY;
  Result := S_OK;
end;

function TOutlookDropTarget.DragLeave: HResult;
begin
  Result := S_OK;
end;

function TOutlookDropTarget.Drop(const dataObj: IDataObject; grfKeyState: Longint; pt: TPoint; var dwEffect: Longint): HResult;
var
  LMailInfo: TOutlookMailInfo;
begin
  dwEffect := DROPEFFECT_COPY;
  FDataObject := dataObj;
  
  try
    // Try to extract mail info from Outlook first
    LMailInfo := ExtractMailInfoFromOutlook;
    
    // If that fails, try to extract from data object
    if not LMailInfo.HasFullInfo then
      LMailInfo := ExtractMailInfoFromDataObject;
    
    // Fire the event
    if Assigned(FOnDropMail) then
      FOnDropMail(Self, LMailInfo);
  except
    // Silently handle errors - the event handler can decide what to do
  end;
  
  Result := S_OK;
end;

function TOutlookDropTarget.ExtractMailInfoFromOutlook: TOutlookMailInfo;
var
  LOutlookApp: OleVariant;
  LSelection: OleVariant;
  LMailItem: OleVariant;
  i: Integer;
  LRecipients: TArray<string>;
  LAttachments: TArray<string>;
  LBody: string;
begin
  // Initialize result
  FillChar(Result, SizeOf(Result), 0);
  Result.HasFullInfo := False;
  
  try
    // Try to get Outlook application
    try
      LOutlookApp := GetActiveOleObject('Outlook.Application');
    except
      LOutlookApp := CreateOleObject('Outlook.Application');
    end;
    
    // Get current selection (the dragged item)
    LSelection := LOutlookApp.ActiveExplorer.Selection;
    if LSelection.Count > 0 then
    begin
      LMailItem := LSelection.Item(1);
      
      // Extract basic information
      Result.SenderName := string(LMailItem.SenderName);
      Result.SenderEmail := string(LMailItem.SenderEmailAddress);
      Result.Subject := string(LMailItem.Subject);
      Result.ReceivedTime := VarToDateTime(LMailItem.ReceivedTime);
      Result.Size := Integer(LMailItem.Size);
      
      // Extract recipients
      if LMailItem.Recipients.Count > 0 then
      begin
        SetLength(LRecipients, LMailItem.Recipients.Count);
        for i := 1 to LMailItem.Recipients.Count do
          LRecipients[i - 1] := Format('%s (%s)', [
            string(LMailItem.Recipients.Item(i).Name),
            string(LMailItem.Recipients.Item(i).Address)
          ]);
        Result.Recipients := LRecipients;
      end;
      
      // Extract attachments
      if LMailItem.Attachments.Count > 0 then
      begin
        SetLength(LAttachments, LMailItem.Attachments.Count);
        for i := 1 to LMailItem.Attachments.Count do
          LAttachments[i - 1] := Format('%s (%d bytes)', [
            string(LMailItem.Attachments.Item(i).FileName),
            Integer(LMailItem.Attachments.Item(i).Size)
          ]);
        Result.Attachments := LAttachments;
      end;
      
      // Extract body preview
      LBody := string(LMailItem.Body);
      if Length(LBody) > C_BODY_PREVIEW_LENGTH then
        LBody := Copy(LBody, 1, C_BODY_PREVIEW_LENGTH) + '...';
      Result.BodyPreview := LBody;
      
      Result.HasFullInfo := True;
    end;
  except
    // If anything fails, we'll fall back to data object extraction
    Result.HasFullInfo := False;
  end;
end;

function TOutlookDropTarget.ExtractMailInfoFromDataObject: TOutlookMailInfo;
var
  LFormatEtc: TFormatEtc;
  LMedium: TStgMedium;
  LFileGroupDesc: PFileGroupDescriptorW;
  LFileName: string;
begin
  // Initialize result
  FillChar(Result, SizeOf(Result), 0);
  Result.HasFullInfo := False;
  
  try
    // Try to extract filename from data object
    LFormatEtc.cfFormat := RegisterClipboardFormat(C_OUTLOOK_MSG_FORMAT);
    LFormatEtc.ptd := nil;
    LFormatEtc.dwAspect := DVASPECT_CONTENT;
    LFormatEtc.lindex := -1;
    LFormatEtc.tymed := TYMED_HGLOBAL;
    
    if FDataObject.GetData(LFormatEtc, LMedium) = S_OK then
    begin
      try
        LFileGroupDesc := GlobalLock(LMedium.hGlobal);
        if Assigned(LFileGroupDesc) and (LFileGroupDesc.cItems > 0) then
        begin
          try
            LFileName := string(LFileGroupDesc.fgd[0].cFileName);
            if Pos('.msg', LowerCase(LFileName)) > 0 then
              LFileName := Copy(LFileName, 1, Pos('.msg', LowerCase(LFileName)) - 1);
            
            Result.Subject := LFileName;
            Result.SenderName := '[Not available]';
            Result.SenderEmail := '[Not available]';
            Result.ReceivedTime := Now;
            Result.HasFullInfo := False;
          finally
            GlobalUnlock(LMedium.hGlobal);
          end;
        end;
      finally
        ReleaseStgMedium(LMedium);
      end;
    end;
  except
    // If extraction fails, leave result empty
  end;
end;

{ TOutlookDragDropManager }

constructor TOutlookDragDropManager.Create(AControl: TWinControl);
begin
  inherited Create;
  FControl := AControl;
  FInitialized := False;
end;

destructor TOutlookDragDropManager.Destroy;
begin
  Finalize;
  inherited Destroy;
end;

procedure TOutlookDragDropManager.Initialize;
begin
  if FInitialized then
    Exit;
    
  try
    OleInitialize(nil);
    FDropTarget := TOutlookDropTarget.Create(FControl);
    FDropTarget.OnDropMail := FOnDropMail;
    RegisterDragDrop(FControl.Handle, FDropTarget);
    FInitialized := True;
  except
    on E: Exception do
      raise Exception.CreateFmt('Failed to initialize Outlook drag drop: %s', [E.Message]);
  end;
end;

procedure TOutlookDragDropManager.Finalize;
begin
  if not FInitialized then
    Exit;
    
  try
    if Assigned(FDropTarget) then
    begin
      RevokeDragDrop(FControl.Handle);
      FDropTarget := nil;
    end;
    OleUninitialize;
    FInitialized := False;
  except
    // Ignore errors during finalization
  end;
end;

procedure TOutlookDragDropManager.SetOnDropMail(const AValue: TOutlookDropEvent);
begin
  FOnDropMail := AValue;
  if Assigned(FDropTarget) then
    FDropTarget.OnDropMail := AValue;
end;

end.
