unit OutlookDragDropExample;

/// <summary>
/// Example unit demonstrating how to use the OutlookDragDrop unit
/// 
/// This shows the minimal code needed to enable Outlook drag and drop
/// functionality in any Delphi application.
/// </summary>

interface

uses
  Winapi.Windows, Winapi.Messages,
  System.SysUtils, System.Variants, System.Classes,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Vcl.StdCtrls,
  OutlookDragDrop;

type
  TFormExample = class(TForm)
    Panel1: TPanel;
    Memo1: TMemo;
    Label1: TLabel;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
  private
    FDragDropManager: TOutlookDragDropManager;
    procedure HandleMailDrop(Sender: TObject; const AMailInfo: TOutlookMailInfo);
  public
    { Public declarations }
  end;

var
  FormExample: TFormExample;

implementation

{$R *.dfm}

{ TFormExample }

procedure TFormExample.FormCreate(Sender: TObject);
begin
  // Initialize the drag drop manager
  FDragDropManager := TOutlookDragDropManager.Create(Panel1);
  FDragDropManager.OnDropMail := HandleMailDrop;
  FDragDropManager.Initialize;
  
  // Setup UI
  Label1.Caption := 'Drag Outlook emails here';
  Memo1.Lines.Clear;
  Memo1.Lines.Add('Ready to receive Outlook emails...');
end;

procedure TFormExample.FormDestroy(Sender: TObject);
begin
  // Cleanup is automatic - the manager handles OLE cleanup in its destructor
  FreeAndNil(FDragDropManager);
end;

procedure TFormExample.HandleMailDrop(Sender: TObject; const AMailInfo: TOutlookMailInfo);
begin
  // This is all you need to handle dropped Outlook emails!
  Memo1.Lines.Add(Format('Received: %s', [AMailInfo.Subject]));
  Memo1.Lines.Add(Format('From: %s <%s>', [AMailInfo.SenderName, AMailInfo.SenderEmail]));
  
  if AMailInfo.HasFullInfo then
    Memo1.Lines.Add(Format('Date: %s', [DateTimeToStr(AMailInfo.ReceivedTime)]))
  else
    Memo1.Lines.Add('(Limited info available)');
    
  Memo1.Lines.Add('---');
end;

end.
